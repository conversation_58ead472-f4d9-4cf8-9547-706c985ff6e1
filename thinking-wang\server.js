const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3011;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// AI API 配置 - 使用用户的Deepseek配置
const AI_CONFIG = {
    apiKey: '***********************************',
    baseURL: 'https://api.deepseek.com/v1',
    model: 'deepseek-chat'
};

// 调用AI API的函数
async function callAI(prompt) {
    try {
        const response = await axios.post(`${AI_CONFIG.baseURL}/chat/completions`, {
            model: AI_CONFIG.model,
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 1000
        }, {
            headers: {
                'Authorization': `Bearer ${AI_CONFIG.apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data.choices[0].message.content;
    } catch (error) {
        console.error('AI API调用错误:', error.response?.data || error.message);
        throw new Error('AI服务暂时不可用，请稍后重试');
    }
}

// 第一部分：深层问题分析
app.post('/api/analyze-deep-problem', async (req, res) => {
    try {
        const { problem } = req.body;
        
        const prompt = `请分析以下问题表面下的深层次问题：

问题：${problem}

请从以下几个维度进行深入分析：
1. 根本原因：这个问题的根本原因可能是什么？
2. 隐藏假设：这个问题背后隐含了哪些假设？
3. 系统性因素：有哪些系统性或结构性因素在起作用？
4. 利益相关者：谁会从现状中受益？谁会受到影响？
5. 更深层的问题：这个问题可能是更大问题的表现吗？

请用中文回答，条理清晰，每个维度用序号标明。`;

        const analysis = await callAI(prompt);
        res.json({ success: true, analysis });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 第二部分：表象与手段分析
app.post('/api/analyze-phenomenon-means', async (req, res) => {
    try {
        const { input, round, previousAnalysis } = req.body;
        
        let prompt;

        if (round === 1) {
            prompt = `请分析以下内容是表象还是手段：

分析对象：${input}

请按以下步骤分析：
1. 首先判断这是"表象"还是"手段"
2. 如果是表象，请分析其本质是什么
3. 如果是手段，请分析其目的是什么
4. 提供具体的分析理由

请用中文回答，格式如下：
判断：[表象/手段]
分析：[详细分析内容]
理由：[分析理由]`;
        } else {
            prompt = `这是第${round}轮分析。请基于用户的理解和思考，继续深入分析：

${previousAnalysis}

请按以下步骤分析：
1. 首先判断用户提到的内容是"表象"还是"手段"
2. 如果是表象，请分析其本质是什么
3. 如果是手段，请分析其目的是什么
4. 结合前面的分析，提供更深层的洞察
5. 提供具体的分析理由

请用中文回答，格式如下：
判断：[表象/手段]
分析：[详细分析内容]
深层洞察：[结合前面分析的更深层理解]
理由：[分析理由]`;
        }

        const analysis = await callAI(prompt);
        res.json({ success: true, analysis, round });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`Thinking Wang 服务器运行在 http://localhost:${PORT}`);
});
