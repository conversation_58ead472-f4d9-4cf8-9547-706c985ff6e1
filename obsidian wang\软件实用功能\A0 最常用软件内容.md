#### 任务 20条菲律宾新闻
python real_news_bilingual.py
请在实现前先创建当前版本的备份（拆分成500行的小部分进行保存），确保可以随时回退到稳定版本。
#### 任务0     C:\Users\<USER>\mineru_env\Scripts\activate.ps1            # 处理PDF文件   magic-pdf -p <PDF文件路径> -o <输出目录>                                         首先打开E：\mcp-test  ,右键  powershell，   
     .\MinerU\mineru_env\Scripts\activate.ps1         
     magic-pdf -p "E:\mcp-test\txt_input" -o "E:\mcp-test\txt_output"
 python demo/demo.py
#### 任务3  babeldoc 对照并 翻译PDF    
您只需要调换 --lang-in 和 --lang-out 就可以中文变英文
【修改文件】Deepseek完整：
		babeldoc --files "E:\mcp-test\translated_input\IEC 61400-12-1  Ed. 1.0  2005-12.pdf" --output "E:\mcp-test\translated_output" --lang-in en --lang-out zh --openai --openai-model "deepseek-chat" --openai-api-key "***********************************" --openai-base-url "https://api.deepseek.com/v1" --translate-table-text 
	 --ocr-workaround
【修改文件Plus /Air/Flash】智谱完整：
	babeldoc --files "E:\mcp-test\translated_input\Reliability Test Run Criteria.pdf" --output "E:\mcp-test\translated_output" --lang-in en --lang-out zh --disable-rich-text-translate --openai --openai-model "GLM-4-Flash" --openai-api-key "7810b54663644292bcfd221e9ec7f0dd.aPV0rs8StKq1FmD0"  --openai-base-url "https://open.bigmodel.cn/api/paas/v4"--translate-table-text 
	
#### 任务6 爬取文章及同目录其它 
python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/mp/appmsgalbum?action=getalbum&__biz=MzI2NTE3NTgyNA==&scene=1&album_id=1503182114693906433&count=3#wechat_redirect" -n 380 -v -o "C:\Users\<USER>\Documents\Obsidian Vault\项目管理" 风机厂家" 菲律宾当地" 

微信好文"         
请问这个语句里的 -n 10 -v ，是代表爬取前10个文件吗，如果是，我爬取完前10个文件后，我想爬取第11个到第100个，我该怎么输入命令呢，如果没有考虑帮我完善一下吧
#### 任务23 做图表
附件中的文件是一个 Echarts 示例，请你保留 Echarts 的完整代码逻辑，帮我数据替换成以下内容，并给我完整 HTML代码
#### 任务1   安装软件
首先认真学习下面网站的内容  
https://github.com/Yuliang-Liu/MonkeyOC，认真思考用何种方法安装，如果有官方提供的Docker配置则优先Docker安装方案，帮我部署这个项目，并且运行这个项目
#### 任务24 n8n    http://localhost:5678 使用 n8n。
https://docs.n8n.io/  您还可以探索 n8n 的工作流模板库，获取灵感和示例：https://n8n.io/workflows/
创建一个 Docker 卷来存储 n8n 数据： docker volume create n8n_data
运行 n8n Docker 容器：docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
http://localhost:5678/settings/usage  就是之前可以用，但是过了几个小时，又不能用了，请帮我启动n8n
deepseek-r1:7b
#### 任务15知识点提取 用于absidian
第一步需求告诉Cursor：帮咱们提取知识点（如果原文是英文的保留中英文），用Obsidian的反向链接格式标注出来，把人名字（保留中文和拼音名）也标注出来，还有这个人的主要观点。其中，那些常规名词不需要标注，比如公司和产品的名称。
第二步就是对每一个知识点，在当前目录下，全都创建一个空白的md文档。##文件名就是知识点的名称。也就是说，每一个知识点都是一条笔记。这样一来，通过反向链接功能，也就是前边的标注，就能把这些单独的知识点和原文串联在一起。
三步就是填充内容。在刚才创建的每一个空白文档内，补上这个知识点的解释，并且要通俗易懂。确定检查是否已经保存成功。
要求：命名的时候加上时间和相关项目名称（如果提到）
#### 任务  Deepseek 排版
输出格式要求：
标题：使用方正小标宋_GBK，字号为二号，加粗。
一级标题：格式为：一、XXX，方正黑体_GBK，三号，首行缩进值2字符，左对齐，行间距28磅，与正文之间不空行，不加粗
二级标题：格式为：（二）XXX，楷体，三号，首行缩进值2字符，左对齐，行间距28磅，与正文之间不空行，不加粗
三级标题：格式为：1.XXX，仿宋，三号，首行缩进值2字符，行间距28磅，左对齐，与正文之间不空行，不加粗
四级标题：格式为：（1）XXX，仿宋，三号，首行缩进值2字符，左对齐，行间距28磅，与正文之间不空行，不加粗
正文格式：仿宋三号，首行缩进值2字符，左对齐，行间距28磅
请按照我给的格式进行排版，输出 html 格式。

#### 任务26 augment 做会议纪要
分别读取E:\mcp-test\obsidian wang\一般会\A1  目录下15个.md文件，并分别对每个文件做详细的会议纪要，要求不要遗漏，要分事情，分人汇总；每个纪要完成后保存在E:\mcp-test\obsidian wang\一般会\A2  目录下，原文件名后加-A.md 文件


#### 任务 24
端午节马上就要到了，帮我写一篇关于南北方粽子区别的文章，文章中要通过表格来对南北区别的对比。然后将你写的文章写入名为南北粽子对比.docx的文档中，保存在E:\mcp-test文件夹中。要注意word文档的排版
#### 任务 在虚拟环境
 cd "**E:\mcp-test"; .\.venv\Scripts\activate;** pip install markitdown-mcp
 
	  python fix_babeldoc_pdf.py  用来清理文件被占用，如果文件失败可以用来清理垃圾
  #### 调用markitdown-mcp 
	  cd "E:\mcp-test"
	.\.venv\Scripts\activate
	python test_markitdown_pdf.py



任务25
1想做一个http格式的程序，占用的端口是3011
2功能是第一步实现网页上有一个窗口，我可以把我想要朗读的内容，copy进去，然后点确定， 窗口里的内容就可以进行朗读，最好是能够实现中英文内容自己进行切换，都同时都能读
3朗读的速度可以控制，可以加快，也可以变慢

任务26
自己的http应用一定要去启动它的服务器之后才能在网页上用，

























第一个，我正在学习某个概念。请你扮演一个严格的教练，用一连串深入浅出的问题来挑战我，找出我理解中的这个漏洞和误区。

第二个呢，就是让AI来当做你的决策，你可以直接来告诉AI说，我正要决定某个某个具体的事情，请像一个战略大师一样，用一系列尖锐的问题来挑战我的思考，特别是那些我可能会完全忽视的角度，还有后果。

第三个技巧就是让AI当你的创意魔法测试官，比如说啊，你可以告诉AI说我有一个某某创意或者某某方案，请你变身成三个不同的角色。那比如说，挑剔的用户，火眼金睛的投资人，还有幸灾乐祸的竞争对手。
这三个不同的角色。然后呢，用最刁钻的问题来挑战我，让我看到这个创意的所有的弱点，

第四就是可以让AI当你的职场进阶的助手，让AI向你去提问，也就是来一波模拟面试，我想进入或者我想晋升特定的岗位或者特定的职业，然后请你扮演一位资深的面试官，用一系列刁钻的问题来面试我。并在我回答问题之后给我评价和改进的建议。

第五个技巧呢，就是让AI来当你的生活全息扫描仪。让AI通过灵魂拷问来揭示你生活中的失衡和矛盾，方法也很简单，直接告诉AI你现在完整的工作和生活状态。请扮演一位睿智的生活导师。用一系列直击痛点的问题来扫描我的生活的各个维度。特别是找出那些我自己都没有意识到的一些矛盾，还有不平衡。




















































obsidian      
1General help 根据标题自动写 Ctrl +Alt +1
2Continue writing 根据内容继续写Ctrl +Alt +2
3Summarize 总结文章内容Ctrl +Alt +3
4Fix spelling and grammar  检查读写语法Ctrl +Alt +4
5 Find action items  写出需要工作项目 Ctrl +Alt +5
6 New System Prompt  新系统提示 Ctrl +Alt +6
Show context menu  显示上下文菜单    Ctrl +Alt +7
注释/取消注释   Ctrl+/           转到标签 #1   Ctrl+1
#### 任务5反复思考
要使用这个工具，您只需要与支持MCP的AI助手（如Claude）进行对话，它将能够利用Sequential Thinking MCP提供的功能来帮助您解决问题。

1帮我查找mcp相关资料，并整理成一份让外行人也能快速看懂的mcp调研报告。
2可以先用sequential-thinking mcp进行思考，
3然后调用playwright mcp进行查找，
4最后再用sequential-thinking mcp检查。

obsidian是一个很好的知识笔记,请帮我找一下,我是一个小白,想从头开始学习用这个软件,我应该分几步,每步应该学点什么,才能尽快的掌握这个软件,并尽快用好这个软件
#### 任务21  pandoc变化格式 md to docx
C:\Users\<USER>\AppData\Local\Pandoc\pandoc.exe
基础转换：pandoc input.md -o output.docx      ##   pandoc /home/<USER>/documents/input.md -o /home/<USER>/documents/output.docx
带模板与参考文档：pandoc input.md --template=custom.docx --reference-docx=style.docx -o output.docx
处理数学公式：pandoc input.md --mathjax -o output.docx
合并两个文件并输出到上级目录  pandoc chapter1.md chapter2.md -o ../final.docx
pandoc -f docx -t markdown -o "Bago City Wind Power Project（150MW）Early work Weekly Progress Report -XX.md" "E:\mcp-test\work\bago weekly\Bago City Wind Power Project（150MW）Early work Weekly Progress Report -XX.docx"
 C:\Users\<USER>\AppData\Local\Pandoc\pandoc.exe -f docx -t markdown -o "Bago City Wind Power Project（150MW）Early work Weekly Progress Report -XX.md" "E:\mcp-test\work\bago  weekly\Bago City Wind Power Project（150MW）Early work  Weekly Progress Report -XX.docx"

任务18
我的地址是Deca Sentrio 的详细地址是：3/F Deca Corporate Center, Panganiban Drive, Misericordia, Blumentritt, Naga City, Camarines Sur 4400, Philippines。我在填写 Smart SlM Registration的注册信息，Province *  CAMARINES SUR； City/Municipality  是NAGA CITY    问？Barangay我应该选下面哪个？

任务26   pandasai - Docker  布置  PandaAI是一个Python平台，可以轻松地以自然语言向您的数据提问。它帮助非技术用户以更自然的方式与他们的数据进行交互，并帮助技术用户在处理数据时节省时间和精力。
OpenAI API 密钥： OPENAI_API_KEY=your_api_key_here
进入项目目录： cd E:\mcp-test\pandas-ai-docker    运行基本示例： docker run -it --rm pandasai-docker    运行高级示例： docker run -it --rm pandasai-docker python advanced_example.py
如果您想使用自己的数据，可以将数据文件挂载到容器中： docker run -it --rm -v E:\your-data-path:/app/data pandasai-docker
使用方法
进入项目目录：cd E:\mcp-test\pandas-ai-docker   运行基本示例：docker run -it --rm -v E:\mcp-test\pandas-ai-docker\data:/app/data pandasai-docker


任务23  rabbitholes  没有成功
https://github.com/AsyncFuncAI/rabbitholes.git  帮我部署这个项目，并且运行这个项目  TAVILY_API_KEY=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
GOOGLE_AI_API_KEY=5930370cf5699853117593a08d89a5f980b9dd4d9de22dbed8a4ce133734a82f


Docker        cd .   cd ..
退出虚拟环境 deactivate      建立虚拟环境  conda activate pandas-ai-env      选项查看所有容器 docker ps -a
检查n8n容器是否在运行：docker ps | Select-String n8n
如果没有看到n8n容器，可以检查所有容器（包括已停止的）：docker ps -a 
如果容器存在但已停止，可以启动它docker start n8n

#### 任务25  crawl4ai    http://localhost:11235/playground   在界面中配置爬取参数并测试
我们已经成功部署了crawl4ai服务，并通过测试脚本验证了它的功能。以下是部署的关键步骤和使用方法：
部署步骤
使用Docker拉取官方镜像：docker pull unclecode/crawl4ai:latest          启动Docker容器：docker run -d -p 11235:11235 --name crawl4ai --shm-size=1g unclecode/crawl4ai:latest
验证服务是否正常运行：curl http://localhost:11235/health              
使用方法          通过交互式界面使用：在浏览器中访问 http://localhost:11235/playground   在界面中配置爬取参数并测试
通过API调用使用：
发送POST请求到 http://localhost:11235/crawl 进行网页爬取        发送POST请求到 http://localhost:11235/screenshot 获取网页截图
发送POST请求到 http://localhost:11235/pdf 生成PDF文档            发送POST请求到 http://localhost:11235/html 获取预处理后的HTML   发送POST请求到 http://localhost:11235/execute_js 执行JavaScript脚本
API请求示例：高级功能
LLM集成：如果需要使用LLM功能（如内容提取），可以创建一个.llm.env文件并挂载到容器中。
深度爬取：可以配置深度爬取参数，爬取整个网站。   自定义提取：可以使用CSS选择器或LLM策略提取结构化数据。
MCP集成：可以通过MCP协议将crawl4ai与Claude Code等AI工具集成。
维护     查看容器日志：docker logs crawl4ai     重启服务：docker restart crawl4ai    停止服务：docker stop crawl4ai     删除容器：docker rm crawl4ai
crawl4ai现在已经成功部署在您的系统上，您可以开始使用它来爬取网页、生成Markdown内容、提取结构化数据等。如果您有任何问题或需要进一步的帮助，请随时告诉我。
#### 任务12 excel数据比较
在这个目录下E:\mcp-test\work   国际no-TSI和TSI区分-不含吊装   E列的WTG Supplier  和  和Jona达成的  G列的Region 进行比较分析，
2可以先用sequential-thinking mcp进行思考，
3然后调用 PostgreSQL数据库mcp进行把两个文件存进去，再进行分析，1先看和Jona达成的  G列的Region哪些是X（就是region主要负责的）哪些是S（就是region支持的），2再看国际no-TSI和TSI区分-不含吊装  G列的Region哪些是X（就是region主要负责的）哪些是S（就是region支持的）3 对这两列进行分析，需要了解这两个哪些项目是不同的，哪些项目是原来是X变成S了，哪些项目原来是S变成X了，还有哪些项目原来国际no-TSI和TSI区分-不含吊装有的，但是在和Jona达成的里没有了的。 最后的结果要把变化的项目对应的事项名称也附带上，然后保存问分析结果3
4最后再用sequential-thinking mcp检查。

#### 任务12 excel  pdf 翻译
PS E:\mcp-test> babeldoc --files ":\mcp-test\translated input\IEC-61400-11-2012.pdf"--output "E:\mcp-test\translated output"--lang-in en--lang-out zh--openai --openai-model "deepseek-chat" --openai-api-key"***********************************"--openai-base-url "https://api.deepseek.com/v1"--translate-table-text
usage: babeldoc [-h] [-c CONFIG] [--version] [--files FILES] [--debug] [--warmup]
                [--rpc-doclayout RPC_DOCLAYOUT]
                [--generate-offline-assets GENERATE_OFFLINE_ASSETS]
                [--restore-offline-assets RESTORE_OFFLINE_ASSETS] [--pages PAGES]
                [--min-text-length MIN_TEXT_LENGTH] [--lang-in LANG_IN] [--lang-out LANG_OUT]       
                [--output OUTPUT] [--qps QPS] [--ignore-cache] [--no-dual] [--no-mono]
                [--formular-font-pattern FORMULAR_FONT_PATTERN]
                [--formular-char-pattern FORMULAR_CHAR_PATTERN] [--split-short-lines]
                [--short-line-split-factor SHORT_LINE_SPLIT_FACTOR] [--skip-clean]
                [--dual-translate-first] [--disable-rich-text-translate] [--enhance-compatibility]  
                [--use-alternating-pages-dual]
                [--watermark-output-mode {watermarked,no_watermark,both}]
                [--max-pages-per-part MAX_PAGES_PER_PART] [--no-watermark]
                [--report-interval REPORT_INTERVAL] [--translate-table-text] [--show-char-box]      
                [--skip-scanned-detection] [--ocr-workaround] [--openai]
                [--openai-model OPENAI_MODEL] [--openai-base-url OPENAI_BASE_URL]
                [--openai-api-key OPENAI_API_KEY]
                用法：babeldoc [-h] [-c 配置文件] [--版本] [--文件 文件列表] [--调试] [--预热]  
                [--rpc文档布局 RPC_DOCLAYOUT]  
                [--生成离线资源 GENERATE_OFFLINE_ASSETS]  
                [--恢复离线资源 RESTORE_OFFLINE_ASSETS] [--页数 PAGES]  
                [--最小文本长度 MIN_TEXT_LENGTH] [--输入语言 LANG_IN] [--输出语言 LANG_OUT]       
                [--输出 OUTPUT] [--每秒查询数 QPS] [--忽略缓存] [--禁用双语] [--禁用单语]  
                [--公式字体模式 FORMULAR_FONT_PATTERN]  
                [--公式字符模式 FORMULAR_CHAR_PATTERN] [--拆分短行]  
                [--短行拆分系数 SHORT_LINE_SPLIT_FACTOR] [--跳过清理]  
                [--优先双语翻译] [--禁用富文本翻译] [--增强兼容性]  
                [--使用交替页双语模式]  
                [--水印输出模式 {加水印,无水印,两者}]  
                [--每部分最大页数 MAX_PAGES_PER_PART] [--无水印]  
                [--报告间隔 REPORT_INTERVAL] [--翻译表格文本] [--显示字符框]      
                [--跳过扫描检测] [--OCR变通方案] [--启用OpenAI]  
                [--OpenAI模型 OPENAI_MODEL] [--OpenAI基础URL OPENAI_BASE_URL]  
                [--OpenAI密钥 OPENAI_API_KEY]  

（注：根据技术文档惯例保留OCR/OpenAI等专有名词不译，参数选项采用"长选项+空格+参数值"的标准格式，保持命令行工具的原生表述风格）
babeldoc: error: unrecognized arguments: zh--openai --openai-api-key***********************************--openai-base-url https://api.deepseek.com/v1



#### 任务23
1请帮我写一个简历，简历内容要求如下：
2我自己的履历是这样的：
3最后，请根据简历写作要求，将我自己已有的简历 ，重新整合，用英文写到如下模版中：
4要写成的模版是：