const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('Starting server...');

const app = express();
const PORT = process.env.PORT || 3021;

console.log('Setting up middleware...');

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

console.log('Setting up routes...');

// 主页路由
app.get('/', (req, res) => {
    console.log('Serving index.html');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 简单的测试路由
app.get('/test', (req, res) => {
    res.json({ message: 'Server is working!' });
});

console.log('Starting to listen on port', PORT);

app.listen(PORT, () => {
    console.log(`Thinking Wang 服务器运行在 http://localhost:${PORT}`);
}).on('error', (err) => {
    console.error('Server error:', err);
});
